import { useState } from "react";
import { useAppContext } from "~/lib/providers/app-context";
import { Bell, Send, TestTube } from "lucide-react";

export function PushNotificationTest() {
  const { pushNotifications } = useAppContext();
  const [testMessage, setTestMessage] = useState({
    title: "Test Notification",
    body: "This is a test push notification from Sphere!",
  });

  const handleSendTestNotification = () => {
    if (pushNotifications.permission === 'granted') {
      pushNotifications.actions.showNotification({
        title: testMessage.title,
        body: testMessage.body,
        icon: '/favicon.ico',
        data: {
          link: '/notifications'
        }
      });
    }
  };

  const handleRequestPermission = async () => {
    await pushNotifications.actions.requestPermission();
  };

  const handleRefreshToken = async () => {
    await pushNotifications.actions.refreshToken();
  };

  const handleRegisterToken = async () => {
    await pushNotifications.actions.registerToken();
  };

  if (!pushNotifications.isSupported) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <div className="flex items-center gap-2">
          <TestTube className="w-5 h-5 text-red-600" />
          <h3 className="font-medium text-red-900">Push Notification Test</h3>
        </div>
        <p className="text-sm text-red-700 mt-2">
          Push notifications are not supported in this browser.
        </p>
      </div>
    );
  }

  return (
    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
      <div className="flex items-center gap-2 mb-4">
        <TestTube className="w-5 h-5 text-blue-600" />
        <h3 className="font-medium text-blue-900">Push Notification Test</h3>
      </div>

      {/* Status Display */}
      <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
        <div>
          <span className="font-medium text-blue-900">Permission:</span>
          <span className={`ml-2 px-2 py-1 rounded text-xs font-medium ${
            pushNotifications.permission === 'granted' 
              ? 'bg-green-100 text-green-800'
              : pushNotifications.permission === 'denied'
              ? 'bg-red-100 text-red-800'
              : 'bg-yellow-100 text-yellow-800'
          }`}>
            {pushNotifications.permission}
          </span>
        </div>
        <div>
          <span className="font-medium text-blue-900">Token:</span>
          <span className={`ml-2 px-2 py-1 rounded text-xs font-medium ${
            pushNotifications.token 
              ? 'bg-green-100 text-green-800'
              : 'bg-gray-100 text-gray-800'
          }`}>
            {pushNotifications.token ? 'Generated' : 'None'}
          </span>
        </div>
        <div>
          <span className="font-medium text-blue-900">Registered:</span>
          <span className={`ml-2 px-2 py-1 rounded text-xs font-medium ${
            pushNotifications.isRegistered 
              ? 'bg-green-100 text-green-800'
              : 'bg-gray-100 text-gray-800'
          }`}>
            {pushNotifications.isRegistered ? 'Yes' : 'No'}
          </span>
        </div>
        <div>
          <span className="font-medium text-blue-900">Loading:</span>
          <span className={`ml-2 px-2 py-1 rounded text-xs font-medium ${
            pushNotifications.isLoading 
              ? 'bg-yellow-100 text-yellow-800'
              : 'bg-gray-100 text-gray-800'
          }`}>
            {pushNotifications.isLoading ? 'Yes' : 'No'}
          </span>
        </div>
      </div>

      {/* Error Display */}
      {pushNotifications.error && (
        <div className="bg-red-100 border border-red-200 rounded p-3 mb-4">
          <p className="text-sm text-red-700">
            <strong>Error:</strong> {pushNotifications.error}
          </p>
        </div>
      )}

      {/* Test Controls */}
      <div className="space-y-3">
        <div className="flex gap-2">
          <button
            onClick={handleRequestPermission}
            disabled={pushNotifications.permission === 'granted' || pushNotifications.isLoading}
            className="px-3 py-2 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Request Permission
          </button>
          <button
            onClick={handleRefreshToken}
            disabled={pushNotifications.permission !== 'granted' || pushNotifications.isLoading}
            className="px-3 py-2 bg-green-600 text-white text-sm rounded hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Refresh Token
          </button>
          <button
            onClick={handleRegisterToken}
            disabled={!pushNotifications.token || pushNotifications.isRegistered || pushNotifications.isRegistering}
            className="px-3 py-2 bg-purple-600 text-white text-sm rounded hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Register Token
          </button>
        </div>

        {/* Test Notification */}
        {pushNotifications.permission === 'granted' && (
          <div className="border-t border-blue-200 pt-3">
            <h4 className="font-medium text-blue-900 mb-2">Send Test Notification</h4>
            <div className="space-y-2">
              <input
                type="text"
                value={testMessage.title}
                onChange={(e) => setTestMessage(prev => ({ ...prev, title: e.target.value }))}
                placeholder="Notification title"
                className="w-full px-3 py-2 border border-gray-300 rounded text-sm"
              />
              <textarea
                value={testMessage.body}
                onChange={(e) => setTestMessage(prev => ({ ...prev, body: e.target.value }))}
                placeholder="Notification body"
                rows={2}
                className="w-full px-3 py-2 border border-gray-300 rounded text-sm"
              />
              <button
                onClick={handleSendTestNotification}
                className="flex items-center gap-2 px-4 py-2 bg-orange-600 text-white text-sm rounded hover:bg-orange-700"
              >
                <Send className="w-4 h-4" />
                Send Test Notification
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Token Display (for debugging) */}
      {pushNotifications.token && (
        <div className="border-t border-blue-200 pt-3 mt-3">
          <h4 className="font-medium text-blue-900 mb-2">FCM Token (for debugging)</h4>
          <textarea
            value={pushNotifications.token}
            readOnly
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded text-xs font-mono bg-gray-50"
          />
        </div>
      )}
    </div>
  );
}
