import { useState, useEffect, useCallback } from "react";
import { <PERSON>, Refresh<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, User } from "lucide-react";
import {
  useNotifications,
  useReadNotification,
} from "~/lib/api/client-queries";
import { useAppContext } from "~/lib/providers/app-context";
import type { Notification } from "~/lib/api/types";

interface ExtendedNotification extends Notification {
  type: "activity" | "announcement";
}

interface StreamNotificationExtended {
  id: string;
  type: "activity" | "announcement";
  title: string;
  body: string;
  isRead: boolean;
  createdAt: string;
  data?: {
    link?: string;
  };
}

type CombinedNotification = ExtendedNotification | StreamNotificationExtended;

type TabType = "activities" | "announcements";

interface NotificationItemProps {
  item: CombinedNotification;
}

function NotificationItem({ item }: NotificationItemProps) {
  const { mutate: markAsRead, isPending } = useReadNotification();

  const formatTimeAgo = (dateString: string): string => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) return "Just now";
    if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60);
      return `${minutes} ${minutes === 1 ? "minute" : "minutes"} ago`;
    }
    if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600);
      return `${hours} ${hours === 1 ? "hour" : "hours"} ago`;
    }
    if (diffInSeconds < 604800) {
      const days = Math.floor(diffInSeconds / 86400);
      return `${days} ${days === 1 ? "day" : "days"} ago`;
    }

    return new Date(dateString).toLocaleString(undefined, {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const handleNotificationClick = () => {
    // Mark notification as read if it's not already read (only for regular notifications)
    if (!item.isRead && "id" in item && typeof item.id === "number") {
      markAsRead({ notificationId: item.id.toString() });
    }

    // Navigate to the link if available
    if (item.data?.link) {
      try {
        // For web app, we can directly navigate or open in same tab
        window.location.href = item.data.link;
      } catch (error) {
        console.error("Failed to navigate to link:", error);
      }
    }
  };

  return (
    <div
      className={`bg-white rounded-lg border border-gray-200 p-4 mb-3 cursor-pointer transition-all hover:shadow-md ${
        isPending ? "opacity-60" : ""
      }`}
      onClick={handleNotificationClick}
    >
      <div className="flex items-start gap-3">
        <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0">
          <Bell className="w-5 h-5 text-purple-600" />
        </div>
        <div className="flex-1 min-w-0">
          <h3 className="font-medium text-gray-900 mb-1">{item.title}</h3>
          <p className="text-sm text-gray-600 mb-2">{item.body}</p>
          <p className="text-xs text-gray-500">
            {formatTimeAgo(item.createdAt)}
          </p>
        </div>
        {!item.isRead && (
          <div className="w-2 h-2 bg-purple-600 rounded-full flex-shrink-0 mt-2" />
        )}
      </div>
    </div>
  );
}

export function NotificationPage() {
  const [activeTab, setActiveTab] = useState<TabType>("activities");
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [streamNotifications, setStreamNotifications] = useState<any[]>([]);
  const [streamLoading, setStreamLoading] = useState(false);
  const { data, isLoading, isError, refetch } = useNotifications();
  const { userId, streamClient } = useAppContext();

  const determineNotificationType = (
    notification: Notification
  ): "activity" | "announcement" => {
    // Logic to determine type based on content or other criteria
    if (notification.data?.link?.includes("live-classes")) {
      return "activity";
    }
    return "announcement";
  };

  const transformStreamNotification = (
    streamNotification: any
  ): StreamNotificationExtended => {
    const getActivityTitle = (notification: any): string => {
      const verb = notification.verb;
      const actorName =
        typeof notification.actor === "string"
          ? notification.actor
          : notification.actor?.data?.name || "Someone";

      switch (verb) {
        case "post":
          return `${actorName} posted`;
        case "like":
          return `${actorName} liked your post`;
        case "comment":
          return `${actorName} commented on your post`;
        case "follow":
          return `${actorName} started following you`;
        default:
          return `${actorName} ${verb}`;
      }
    };

    const getActivityBody = (notification: any): string => {
      if (typeof notification.object === "string") {
        return notification.object;
      }
      if (notification.object?.message) {
        return notification.object.message;
      }
      if (notification.object?.text) {
        return notification.object.text;
      }
      return `New ${notification.verb} activity`;
    };

    return {
      id: streamNotification.id,
      type: "announcement",
      title: getActivityTitle(streamNotification),
      body: getActivityBody(streamNotification),
      isRead: streamNotification.is_read || false,
      createdAt: streamNotification.time,
      data: {
        link: streamNotification.foreign_id || undefined,
      },
    };
  };

  const fetchStreamNotifications = useCallback(async () => {
    if (!streamClient) {
      setStreamNotifications([]);
      return;
    }

    setStreamLoading(true);
    try {
      const notificationFeed = streamClient.feed("notification", userId);
      const response = await notificationFeed.get({
        limit: 50,
        withReactionCounts: true,
        withOwnReactions: true,
      });

      setStreamNotifications(response.results || []);
    } catch (error) {
      console.error("Error fetching Stream notifications:", error);
      setStreamNotifications([]);
    } finally {
      setStreamLoading(false);
    }
  }, [streamClient]);

  useEffect(() => {
    fetchStreamNotifications();
  }, [fetchStreamNotifications]);

  const filteredNotifications = useCallback(() => {
    const regularNotifications: ExtendedNotification[] =
      data?.data?.notifications?.map((notification) => ({
        ...notification,
        type: determineNotificationType(notification),
      })) || [];

    const transformedStreamNotifications: StreamNotificationExtended[] =
      streamNotifications?.map(transformStreamNotification) || [];

    const allNotifications: CombinedNotification[] = [
      ...regularNotifications,
      ...transformedStreamNotifications,
    ];

    allNotifications.sort((a, b) => {
      const dateA = new Date(a.createdAt);
      const dateB = new Date(b.createdAt);
      return dateB.getTime() - dateA.getTime();
    });

    return allNotifications.filter((notification) =>
      activeTab === "activities"
        ? notification.type === "activity"
        : notification.type === "announcement"
    );
  }, [data, streamNotifications, activeTab]);

  const renderEmptyState = () => {
    if (isLoading || streamLoading) {
      return (
        <div className="flex flex-col items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mb-4"></div>
          <p className="text-gray-500">Loading notifications...</p>
        </div>
      );
    }

    if (isError) {
      return (
        <div className="flex flex-col items-center justify-center py-12">
          <AlertTriangle className="w-12 h-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Something went wrong
          </h3>
          <p className="text-gray-500 mb-4">
            We couldn't load your notifications
          </p>
        </div>
      );
    }

    return (
      <div className="flex flex-col items-center justify-center py-12">
        <Bell className="w-12 h-12 text-gray-400 mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No News Yet</h3>
        <p className="text-gray-500">
          We'll notify you as soon as we hear anything
        </p>
      </div>
    );
  };

  const notifications = filteredNotifications();

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="relative w-full h-48 bg-gradient-to-r from-purple-600 to-pink-600">
        <div className="absolute inset-0 bg-black/20" />
        <div className="relative z-10 flex flex-col justify-end h-full p-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-white">Notifications</h1>
              <p className="text-white/80 mt-2">
                Stay updated with your activities
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-auto bg-gray-50">
        <div className="max-w-2xl mx-auto p-6">
          {/* Tabs */}
          <div className="flex gap-2 mb-6">
            <button
              onClick={() => setActiveTab("activities")}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                activeTab === "activities"
                  ? "bg-white text-gray-900 shadow-sm"
                  : "bg-gray-200 text-gray-600 hover:bg-gray-300"
              }`}
            >
              Activities
            </button>
            <button
              onClick={() => setActiveTab("announcements")}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                activeTab === "announcements"
                  ? "bg-white text-gray-900 shadow-sm"
                  : "bg-gray-200 text-gray-600 hover:bg-gray-300"
              }`}
            >
              Announcements
            </button>
          </div>

          {/* Notifications */}
          {notifications.length > 0 ? (
            <div className="space-y-3">
              {notifications.map((notification) => (
                <NotificationItem
                  key={
                    typeof notification.id === "string"
                      ? notification.id
                      : notification.id.toString()
                  }
                  item={notification}
                />
              ))}
            </div>
          ) : (
            renderEmptyState()
          )}
        </div>
      </div>
    </div>
  );
}
