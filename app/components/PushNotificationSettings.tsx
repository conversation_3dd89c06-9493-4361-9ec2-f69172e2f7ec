import { useState } from "react";
import { <PERSON>, Bell<PERSON>ff, <PERSON><PERSON><PERSON>, AlertCircle, CheckCircle, Loader2 } from "lucide-react";
import { useAppContext } from "~/lib/providers/app-context";

export function PushNotificationSettings() {
  const { pushNotifications } = useAppContext();
  const [isRequesting, setIsRequesting] = useState(false);

  const handleRequestPermission = async () => {
    setIsRequesting(true);
    try {
      const granted = await pushNotifications.actions.requestPermission();
      if (granted) {
        // Token will be automatically registered via the app context effect
        console.log('Push notifications enabled successfully');
      }
    } catch (error) {
      console.error('Failed to enable push notifications:', error);
    } finally {
      setIsRequesting(false);
    }
  };

  const handleRefreshToken = async () => {
    try {
      await pushNotifications.actions.refreshToken();
    } catch (error) {
      console.error('Failed to refresh token:', error);
    }
  };

  const getPermissionStatus = () => {
    switch (pushNotifications.permission) {
      case 'granted':
        return {
          icon: CheckCircle,
          text: 'Enabled',
          color: 'text-green-600',
          bgColor: 'bg-green-100',
        };
      case 'denied':
        return {
          icon: BellOff,
          text: 'Blocked',
          color: 'text-red-600',
          bgColor: 'bg-red-100',
        };
      default:
        return {
          icon: AlertCircle,
          text: 'Not Set',
          color: 'text-yellow-600',
          bgColor: 'bg-yellow-100',
        };
    }
  };

  const status = getPermissionStatus();
  const StatusIcon = status.icon;

  if (!pushNotifications.isSupported) {
    return (
      <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
            <BellOff className="w-5 h-5 text-gray-500" />
          </div>
          <div className="flex-1">
            <h3 className="font-medium text-gray-900">Push Notifications</h3>
            <p className="text-sm text-gray-600">
              Not supported in this browser
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg p-4 border border-gray-200">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className={`w-10 h-10 ${status.bgColor} rounded-full flex items-center justify-center`}>
            <StatusIcon className={`w-5 h-5 ${status.color}`} />
          </div>
          <div className="flex-1">
            <h3 className="font-medium text-gray-900">Push Notifications</h3>
            <p className="text-sm text-gray-600">
              Get notified about important updates
            </p>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <span className={`text-sm font-medium ${status.color}`}>
            {status.text}
          </span>
          
          {pushNotifications.permission === 'default' && (
            <button
              onClick={handleRequestPermission}
              disabled={isRequesting || pushNotifications.isLoading}
              className="px-4 py-2 bg-purple-600 text-white text-sm font-medium rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
            >
              {(isRequesting || pushNotifications.isLoading) ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin" />
                  Enabling...
                </>
              ) : (
                <>
                  <Bell className="w-4 h-4" />
                  Enable
                </>
              )}
            </button>
          )}
          
          {pushNotifications.permission === 'granted' && (
            <button
              onClick={handleRefreshToken}
              disabled={pushNotifications.isLoading}
              className="px-3 py-2 bg-gray-100 text-gray-700 text-sm font-medium rounded-lg hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed"
              title="Refresh notification token"
            >
              <Settings className="w-4 h-4" />
            </button>
          )}
        </div>
      </div>

      {/* Status details */}
      {pushNotifications.permission === 'granted' && (
        <div className="mt-4 pt-4 border-t border-gray-100">
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600">Status:</span>
            <div className="flex items-center gap-2">
              {pushNotifications.token ? (
                <span className="text-green-600 flex items-center gap-1">
                  <CheckCircle className="w-3 h-3" />
                  Connected
                </span>
              ) : (
                <span className="text-yellow-600 flex items-center gap-1">
                  <AlertCircle className="w-3 h-3" />
                  Connecting...
                </span>
              )}
              
              {pushNotifications.isRegistered && (
                <span className="text-green-600 text-xs">• Registered</span>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Error state */}
      {pushNotifications.error && (
        <div className="mt-4 pt-4 border-t border-gray-100">
          <div className="flex items-center gap-2 text-sm text-red-600">
            <AlertCircle className="w-4 h-4" />
            <span>{pushNotifications.error}</span>
          </div>
        </div>
      )}

      {/* Instructions for blocked notifications */}
      {pushNotifications.permission === 'denied' && (
        <div className="mt-4 pt-4 border-t border-gray-100">
          <div className="text-sm text-gray-600">
            <p className="mb-2">To enable notifications:</p>
            <ol className="list-decimal list-inside space-y-1 text-xs">
              <li>Click the lock icon in your browser's address bar</li>
              <li>Change notifications from "Block" to "Allow"</li>
              <li>Refresh this page</li>
            </ol>
          </div>
        </div>
      )}
    </div>
  );
}

export function PushNotificationBanner() {
  const { pushNotifications } = useAppContext();
  const [isDismissed, setIsDismissed] = useState(false);

  // Don't show banner if notifications are not supported, already granted, or dismissed
  if (
    !pushNotifications.isSupported ||
    pushNotifications.permission === 'granted' ||
    pushNotifications.permission === 'denied' ||
    isDismissed
  ) {
    return null;
  }

  const handleEnable = async () => {
    try {
      await pushNotifications.actions.requestPermission();
    } catch (error) {
      console.error('Failed to enable push notifications:', error);
    }
  };

  return (
    <div className="bg-purple-50 border border-purple-200 rounded-lg p-4 mb-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
            <Bell className="w-4 h-4 text-purple-600" />
          </div>
          <div>
            <h4 className="font-medium text-purple-900">Enable Push Notifications</h4>
            <p className="text-sm text-purple-700">
              Stay updated with important announcements and activities
            </p>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <button
            onClick={handleEnable}
            disabled={pushNotifications.isLoading}
            className="px-4 py-2 bg-purple-600 text-white text-sm font-medium rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {pushNotifications.isLoading ? 'Enabling...' : 'Enable'}
          </button>
          <button
            onClick={() => setIsDismissed(true)}
            className="px-3 py-2 text-purple-600 text-sm font-medium rounded-lg hover:bg-purple-100"
          >
            Dismiss
          </button>
        </div>
      </div>
    </div>
  );
}
