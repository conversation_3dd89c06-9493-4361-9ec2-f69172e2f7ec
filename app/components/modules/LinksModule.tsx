import { useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Link2, FileText } from "lucide-react";
import type { LinksModule as LinksModuleType } from "~/lib/api/types";
import { useMyGroups } from "~/lib/api/client-queries";

interface Props {
  module: LinksModuleType;
  params: {
    groupId: string;
    cohortId: string;
    moduleId: string;
  };
}

type FilterType = "All" | "Links" | "Attachments";

export default function LinksModule({ module, params }: Props) {
  const [activeFilter, setActiveFilter] = useState<FilterType>("All");

  // Fetch group data to get banner image
  const { data: myGroupsResponse } = useMyGroups();
  const group = myGroupsResponse?.groups.byId[params.groupId];

  const filterTabs: FilterType[] = ["All", "Links", "Attachments"];

  const filteredAttachments = module.config.attachments.filter((attachment) => {
    if (activeFilter === "All") return true;
    if (activeFilter === "Links") return attachment.attachmentType === "link";
    return attachment.attachmentType === "document";
  });

  const getIconStyles = (type: "link" | "document") => {
    if (type === "link") {
      return "bg-zinc-600";
    }
    return "bg-zinc-700";
  };

  const handleOpenLink = (url: string) => {
    window.open(url, "_blank", "noopener,noreferrer");
  };

  // Calculate counts for display
  const totalLinks = module.config.attachments.filter(
    (a) => a.attachmentType === "link"
  ).length;
  const totalAttachments = module.config.attachments.filter(
    (a) => a.attachmentType === "document"
  ).length;

  return (
    <div className="bg-black min-h-screen">
      {/* Top Bar */}
      <div className="sticky top-0 z-50 bg-black/10 backdrop-blur-md border-b border-zinc-800">
        <div className="max-w-4xl mx-auto px-8 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-xl font-semibold text-white">
                {module.name}
              </h1>
              <p className="text-sm text-zinc-400">
                {totalLinks + totalAttachments} Items
              </p>
            </div>
            <button className="p-2 hover:bg-zinc-800 rounded-lg transition-colors">
              <MoreHorizontal className="w-5 h-5 text-zinc-400 flex-shrink-0" />
            </button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto px-8 py-12">
        {/* Filter Tabs */}
        <div className="flex gap-2 mb-8">
          {filterTabs.map((filter) => (
            <button
              key={filter}
              onClick={() => setActiveFilter(filter)}
              className={`px-4 py-1.5 rounded-full text-sm font-medium transition-colors ${
                activeFilter === filter
                  ? "bg-white text-black"
                  : "bg-zinc-800 text-zinc-400 hover:text-zinc-300"
              }`}
            >
              {filter}
            </button>
          ))}
        </div>

        {/* Links/Attachments List */}
        <div className="space-y-3">
          {filteredAttachments.map((attachment, index) => (
            <button
              key={`${attachment.title}-${index}`}
              onClick={() => handleOpenLink(attachment.url)}
              className="w-full flex items-center gap-3 p-4 hover:bg-zinc-800/50 bg-zinc-800/30 rounded-lg transition-colors group"
            >
              {/* Icon */}
              <div
                className={`w-10 h-10 rounded-lg flex items-center justify-center flex-shrink-0 ${getIconStyles(
                  attachment.attachmentType
                )}`}
              >
                {attachment.attachmentType === "link" ? (
                  <Link2 className="w-5 h-5 text-white" />
                ) : (
                  <FileText className="w-5 h-5 text-white" />
                )}
              </div>

              {/* Content */}
              <div className="flex-1 text-left min-w-0">
                <h3 className="text-white font-medium text-base group-hover:text-gray-100">
                  {attachment.title}
                </h3>
                <p className="text-zinc-400 text-sm truncate mt-0.5">
                  {attachment.url}
                </p>
              </div>
            </button>
          ))}

          {filteredAttachments.length === 0 && (
            <div className="text-center py-12">
              <p className="text-zinc-400">
                No{" "}
                {activeFilter === "All" ? "items" : activeFilter.toLowerCase()}{" "}
                found
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
