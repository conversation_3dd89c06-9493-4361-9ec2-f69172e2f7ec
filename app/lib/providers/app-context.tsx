import {
  createContext,
  useContext,
  useEffect,
  useState,
  type <PERSON>actN<PERSON>,
} from "react";
import Session from "supertokens-web-js/recipe/session";
import { useGenerateGetStreamToken } from "~/lib/api/client-queries";
import { queryClient } from "~/lib/providers/query-client";
import type { StreamChat, Channel, Thread } from "stream-chat";
import { connect } from "getstream";

const GROUP_KEY = "current_group_id";
const COHORT_KEY = "current_cohort_id";
const STREAM_API_KEY = import.meta.env.VITE_STREAM_API_KEY as
  | string
  | undefined;

if (!STREAM_API_KEY) {
  // eslint-disable-next-line no-console
  console.warn(
    "VITE_STREAM_API_KEY is not defined – Stream chat will be disabled."
  );
}

export type AppContextType = {
  chatClient: StreamChat | null;
  streamClient: any | null;
  userId: string | null;
  groupId: string | null;
  cohortId: string | null;
  streamToken: string | null;
  thread: Thread | null;
  channel: Channel | null;
  setUserId: (id: string | null) => void;
  setGroupId: (id: string | null) => void;
  setCohortId: (id: string | null) => void;
  setThread: (thread: Thread | null) => void;
  setChannel: (channel: Channel | null) => void;
  cleanup: () => void;
  refreshAuth: () => Promise<void>;
};

const AppContext = createContext<AppContextType | undefined>(undefined);

export function AppProvider({ children }: { children: ReactNode }) {
  const [userId, setUserId] = useState<string | null>(null);
  const [groupId, setGroupId] = useState<string | null>(null);
  const [cohortId, setCohortId] = useState<string | null>(null);
  const [thread, setThread] = useState<Thread | null>(null);
  const [channel, setChannel] = useState<Channel | null>(null);
  const [chatClient, setChatClient] = useState<StreamChat | null>(null);
  const [streamClient, setStreamClient] = useState<any | null>(null);

  const { mutate: generateToken, data: tokenData } =
    useGenerateGetStreamToken();

  // Convert API response shape → token string or null
  const streamToken: string | null = tokenData?.data?.token ?? null;

  // Fetch auth status & user id via SuperTokens once on mount
  useEffect(() => {
    let mounted = true;

    Session.doesSessionExist()
      .then((exist) => {
        if (!exist || !mounted) return;
        return Session.getUserId();
      })
      .then((id) => {
        if (id && mounted) {
          setUserId(id);
          // trigger token generation
          generateToken();
        }
      })
      .catch((err) => {
        // Failed to initialize auth
      });

    return () => {
      mounted = false;
    };
  }, []);

  // Load persisted IDs
  useEffect(() => {
    const storedGroupId = localStorage.getItem(GROUP_KEY);
    if (storedGroupId) setGroupId(storedGroupId);
    const storedCohortId = localStorage.getItem(COHORT_KEY);
    if (storedCohortId) setCohortId(storedCohortId);
  }, []);

  // Dynamically create StreamChat client in the browser once we have auth
  useEffect(() => {
    if (!userId || !streamToken || !STREAM_API_KEY) return;

    let client: StreamChat;

    (async () => {
      const { StreamChat } = await import("stream-chat");
      client = StreamChat.getInstance(STREAM_API_KEY);
      await client.connectUser({ id: userId }, streamToken);
      setChatClient(client);
    })();

    return () => {
      if (client) {
        client.disconnectUser().catch(() => {});
      }
    };
    // Only when tokens or user change
  }, [userId, streamToken]);

  // Initialize GetStream client for feeds and notifications
  useEffect(() => {
    if (!userId || !streamToken || !STREAM_API_KEY) return;

    try {
      const client = connect(
        STREAM_API_KEY,
        streamToken,
        import.meta.env.VITE_STREAM_APP_ID
      );
      setStreamClient(client);
    } catch (error) {
      console.error("Failed to initialize GetStream client:", error);
    }
  }, [userId, streamToken]);

  const setGroupIdWithStorage = (id: string | null) => {
    if (id) {
      localStorage.setItem(GROUP_KEY, id);
    } else {
      localStorage.removeItem(GROUP_KEY);
    }
    setGroupId(id);
  };

  const setCohortIdWithStorage = (id: string | null) => {
    if (id) {
      localStorage.setItem(COHORT_KEY, id);
    } else {
      localStorage.removeItem(COHORT_KEY);
    }
    setCohortId(id);
  };

  const cleanup = () => {
    localStorage.removeItem(GROUP_KEY);
    localStorage.removeItem(COHORT_KEY);
    setGroupId(null);
    setCohortId(null);
    setUserId(null);
    queryClient.removeQueries();
  };

  const refreshAuth = async () => {
    try {
      const exists = await Session.doesSessionExist();
      if (exists) {
        const id = await Session.getUserId();
        if (id) {
          setUserId(id);
          generateToken();
        }
      }
    } catch (err) {
      console.error("Failed to refresh auth:", err);
    }
  };

  return (
    <AppContext.Provider
      value={{
        chatClient,
        streamClient,
        userId,
        groupId,
        cohortId,
        thread,
        channel,
        streamToken,
        setUserId,
        setGroupId: setGroupIdWithStorage,
        setCohortId: setCohortIdWithStorage,
        setThread,
        setChannel,
        cleanup,
        refreshAuth,
      }}
    >
      {children}
    </AppContext.Provider>
  );
}

export function useAppContext() {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error("useAppContext must be used within an AppProvider");
  }
  return context;
}
