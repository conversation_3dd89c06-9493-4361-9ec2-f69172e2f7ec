import { initializeApp, getApps, getApp } from "firebase/app";
import { getMessaging, isSupported } from "firebase/messaging";

// Firebase configuration from environment variables
const firebaseConfig = {
  apiKey: import.meta.env.VITE_FIREBASE_API_KEY,
  authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN,
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID,
  storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
  appId: import.meta.env.VITE_FIREBASE_APP_ID,
};

// VAPID key for web push notifications
export const VAPID_KEY = import.meta.env.VITE_FIREBASE_VAPID_KEY;

// Initialize Firebase app (singleton pattern)
export const firebaseApp =
  getApps().length === 0 ? initializeApp(firebaseConfig) : getApp();

// Initialize Firebase messaging (only in browser environment)
export const getFirebaseMessaging = async () => {
  if (typeof window === "undefined") {
    return null;
  }

  try {
    const supported = await isSupported();
    if (!supported) {
      console.warn("Firebase messaging is not supported in this browser");
      return null;
    }

    return getMessaging(firebaseApp);
  } catch (error) {
    console.error("Error initializing Firebase messaging:", error);
    return null;
  }
};

// Validate Firebase configuration
export const validateFirebaseConfig = (): boolean => {
  console.log("🔄 Validating Firebase configuration...");

  const requiredEnvVars = [
    "VITE_FIREBASE_API_KEY",
    "VITE_FIREBASE_AUTH_DOMAIN",
    "VITE_FIREBASE_PROJECT_ID",
    "VITE_FIREBASE_STORAGE_BUCKET",
    "VITE_FIREBASE_MESSAGING_SENDER_ID",
    "VITE_FIREBASE_APP_ID",
    "VITE_FIREBASE_VAPID_KEY",
  ];

  const missingVars = requiredEnvVars.filter(
    (varName) => !import.meta.env[varName]
  );

  if (missingVars.length > 0) {
    console.error("❌ Missing Firebase environment variables:", missingVars);
    console.error("💡 Add these to your .env file:");
    missingVars.forEach((varName) => {
      console.error(`   ${varName}="your-value"`);
    });
    return false;
  }

  console.log("✅ All Firebase environment variables are configured");

  // Log the configuration (without sensitive values)
  console.log("📋 Firebase config summary:");
  console.log(`   Project ID: ${import.meta.env.VITE_FIREBASE_PROJECT_ID}`);
  console.log(`   Auth Domain: ${import.meta.env.VITE_FIREBASE_AUTH_DOMAIN}`);
  console.log(
    `   VAPID Key: ${import.meta.env.VITE_FIREBASE_VAPID_KEY?.substring(
      0,
      20
    )}...`
  );

  return true;
};
