import { getToken, onMessage, type Messaging } from 'firebase/messaging';
import { getFirebaseMessaging, VAPID_KEY } from './config';

export type NotificationPermission = 'default' | 'granted' | 'denied';

export interface PushNotificationPayload {
  title?: string;
  body?: string;
  icon?: string;
  badge?: string;
  image?: string;
  data?: Record<string, any>;
  actions?: Array<{
    action: string;
    title: string;
    icon?: string;
  }>;
}

/**
 * Request notification permission from the user
 */
export const requestNotificationPermission = async (): Promise<NotificationPermission> => {
  if (typeof window === 'undefined' || !('Notification' in window)) {
    console.warn('Notifications are not supported in this environment');
    return 'denied';
  }

  try {
    const permission = await Notification.requestPermission();
    return permission as NotificationPermission;
  } catch (error) {
    console.error('Error requesting notification permission:', error);
    return 'denied';
  }
};

/**
 * Get the current notification permission status
 */
export const getNotificationPermission = (): NotificationPermission => {
  if (typeof window === 'undefined' || !('Notification' in window)) {
    return 'denied';
  }
  return Notification.permission as NotificationPermission;
};

/**
 * Get Firebase messaging token for push notifications
 */
export const getMessagingToken = async (): Promise<string | null> => {
  try {
    const messaging = await getFirebaseMessaging();
    if (!messaging) {
      console.warn('Firebase messaging not available');
      return null;
    }

    if (!VAPID_KEY) {
      console.error('VAPID key not configured');
      return null;
    }

    // Check if permission is granted
    const permission = getNotificationPermission();
    if (permission !== 'granted') {
      console.warn('Notification permission not granted');
      return null;
    }

    const token = await getToken(messaging, {
      vapidKey: VAPID_KEY,
    });

    if (token) {
      console.log('FCM token generated:', token);
      return token;
    } else {
      console.warn('No registration token available');
      return null;
    }
  } catch (error) {
    console.error('Error getting messaging token:', error);
    return null;
  }
};

/**
 * Set up foreground message listener
 */
export const onMessageListener = (
  callback: (payload: PushNotificationPayload) => void
): (() => void) | null => {
  let unsubscribe: (() => void) | null = null;

  getFirebaseMessaging().then((messaging) => {
    if (!messaging) {
      console.warn('Firebase messaging not available for foreground listener');
      return;
    }

    unsubscribe = onMessage(messaging, (payload) => {
      console.log('Foreground message received:', payload);
      
      const notification: PushNotificationPayload = {
        title: payload.notification?.title,
        body: payload.notification?.body,
        icon: payload.notification?.icon,
        image: payload.notification?.image,
        data: payload.data,
      };

      callback(notification);
    });
  }).catch((error) => {
    console.error('Error setting up foreground message listener:', error);
  });

  // Return cleanup function
  return () => {
    if (unsubscribe) {
      unsubscribe();
    }
  };
};

/**
 * Setup background message handler (requires service worker)
 */
export const setupBackgroundMessageHandler = (): void => {
  if (typeof window === 'undefined' || !('serviceWorker' in navigator)) {
    console.warn('Service workers not supported');
    return;
  }

  // Register service worker for background messages
  navigator.serviceWorker
    .register('/firebase-messaging-sw.js')
    .then((registration) => {
      console.log('Service worker registered successfully:', registration);
    })
    .catch((error) => {
      console.error('Service worker registration failed:', error);
    });
};

/**
 * Show a local notification (fallback for when app is in foreground)
 */
export const showLocalNotification = (payload: PushNotificationPayload): void => {
  if (typeof window === 'undefined' || !('Notification' in window)) {
    console.warn('Notifications not supported');
    return;
  }

  if (getNotificationPermission() !== 'granted') {
    console.warn('Notification permission not granted');
    return;
  }

  const { title = 'Sphere', body, icon, badge, image, data, actions } = payload;

  const options: NotificationOptions = {
    body,
    icon: icon || '/favicon.ico',
    badge: badge || '/favicon.ico',
    image,
    data,
    actions,
    requireInteraction: false,
    silent: false,
  };

  try {
    const notification = new Notification(title, options);
    
    // Handle notification click
    notification.onclick = (event) => {
      event.preventDefault();
      window.focus();
      
      // Handle navigation based on data
      if (data?.link) {
        window.location.href = data.link;
      }
      
      notification.close();
    };

    // Auto-close after 5 seconds
    setTimeout(() => {
      notification.close();
    }, 5000);
  } catch (error) {
    console.error('Error showing local notification:', error);
  }
};
