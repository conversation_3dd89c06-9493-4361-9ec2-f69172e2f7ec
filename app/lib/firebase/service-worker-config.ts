/**
 * Utility to configure the Firebase messaging service worker with environment variables
 * This should be called during the build process or app initialization
 */

export const configureServiceWorker = async (): Promise<void> => {
  if (typeof window === 'undefined') {
    return;
  }

  try {
    // Fetch the service worker file
    const response = await fetch('/firebase-messaging-sw.js');
    let swContent = await response.text();

    // Replace placeholders with actual environment variables
    const replacements = {
      'VITE_FIREBASE_API_KEY_PLACEHOLDER': import.meta.env.VITE_FIREBASE_API_KEY || '',
      'VITE_FIREBASE_AUTH_DOMAIN_PLACEHOLDER': import.meta.env.VITE_FIREBASE_AUTH_DOMAIN || '',
      'VITE_FIREBASE_PROJECT_ID_PLACEHOLDER': import.meta.env.VITE_FIREBASE_PROJECT_ID || '',
      'VITE_FIREBASE_STORAGE_BUCKET_PLACEHOLDER': import.meta.env.VITE_FIREBASE_STORAGE_BUCKET || '',
      'VITE_FIREBASE_MESSAGING_SENDER_ID_PLACEHOLDER': import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID || '',
      'VITE_FIREBASE_APP_ID_PLACEHOLDER': import.meta.env.VITE_FIREBASE_APP_ID || ''
    };

    // Apply replacements
    Object.entries(replacements).forEach(([placeholder, value]) => {
      swContent = swContent.replace(new RegExp(placeholder, 'g'), value);
    });

    // Create a blob with the configured content
    const blob = new Blob([swContent], { type: 'application/javascript' });
    const swUrl = URL.createObjectURL(blob);

    // Register the configured service worker
    if ('serviceWorker' in navigator) {
      const registration = await navigator.serviceWorker.register(swUrl);
      console.log('Firebase messaging service worker registered:', registration);
      
      // Clean up the blob URL
      URL.revokeObjectURL(swUrl);
      
      return registration;
    }
  } catch (error) {
    console.error('Error configuring service worker:', error);
    
    // Fallback: try to register the original service worker
    if ('serviceWorker' in navigator) {
      try {
        const registration = await navigator.serviceWorker.register('/firebase-messaging-sw.js');
        console.log('Fallback service worker registered:', registration);
        return registration;
      } catch (fallbackError) {
        console.error('Fallback service worker registration failed:', fallbackError);
      }
    }
  }
};

/**
 * Alternative approach: Generate service worker content dynamically
 */
export const generateServiceWorkerContent = (): string => {
  return `
// Firebase messaging service worker for background notifications
importScripts('https://www.gstatic.com/firebasejs/11.10.0/firebase-app-compat.js');
importScripts('https://www.gstatic.com/firebasejs/11.10.0/firebase-messaging-compat.js');

const firebaseConfig = {
  apiKey: "${import.meta.env.VITE_FIREBASE_API_KEY || ''}",
  authDomain: "${import.meta.env.VITE_FIREBASE_AUTH_DOMAIN || ''}", 
  projectId: "${import.meta.env.VITE_FIREBASE_PROJECT_ID || ''}",
  storageBucket: "${import.meta.env.VITE_FIREBASE_STORAGE_BUCKET || ''}",
  messagingSenderId: "${import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID || ''}",
  appId: "${import.meta.env.VITE_FIREBASE_APP_ID || ''}"
};

firebase.initializeApp(firebaseConfig);
const messaging = firebase.messaging();

messaging.onBackgroundMessage((payload) => {
  console.log('Background message received:', payload);

  const notificationTitle = payload.notification?.title || 'Sphere';
  const notificationOptions = {
    body: payload.notification?.body || 'You have a new notification',
    icon: payload.notification?.icon || '/favicon.ico',
    badge: '/favicon.ico',
    image: payload.notification?.image,
    data: payload.data || {},
    actions: [
      { action: 'open', title: 'Open App' },
      { action: 'close', title: 'Dismiss' }
    ],
    requireInteraction: true,
    silent: false
  };

  self.registration.showNotification(notificationTitle, notificationOptions);
});

self.addEventListener('notificationclick', (event) => {
  event.notification.close();
  
  if (event.action === 'close') return;

  event.waitUntil(
    clients.matchAll({ type: 'window', includeUncontrolled: true }).then((clientList) => {
      const data = event.notification.data || {};
      const targetUrl = data.link || '/notifications';

      for (const client of clientList) {
        if (client.url.includes(self.location.origin) && 'focus' in client) {
          if (data.link) {
            client.postMessage({ type: 'NOTIFICATION_CLICK', url: targetUrl });
          }
          return client.focus();
        }
      }

      if (clients.openWindow) {
        return clients.openWindow(self.location.origin + targetUrl);
      }
    })
  );
});

self.addEventListener('install', (event) => {
  console.log('Firebase messaging service worker installed');
  self.skipWaiting();
});

self.addEventListener('activate', (event) => {
  console.log('Firebase messaging service worker activated');
  event.waitUntil(self.clients.claim());
});
`;
};
