import { useState, useEffect, useCallback, useRef } from 'react';
import {
  requestNotificationPermission,
  getNotificationPermission,
  getMessagingToken,
  onMessageListener,
  setupBackgroundMessageHandler,
  showLocalNotification,
  type NotificationPermission,
  type PushNotificationPayload
} from '~/lib/firebase/messaging';
import { validateFirebaseConfig } from '~/lib/firebase/config';
import { useRegisterPushNotificationToken } from '~/lib/api/client-queries';

export interface PushNotificationState {
  permission: NotificationPermission;
  token: string | null;
  isSupported: boolean;
  isLoading: boolean;
  error: string | null;
  isRegistered: boolean;
}

export interface PushNotificationActions {
  requestPermission: () => Promise<boolean>;
  refreshToken: () => Promise<void>;
  registerToken: () => Promise<void>;
  showNotification: (payload: PushNotificationPayload) => void;
}

export const usePushNotifications = () => {
  const [state, setState] = useState<PushNotificationState>({
    permission: 'default',
    token: null,
    isSupported: false,
    isLoading: true,
    error: null,
    isRegistered: false
  });

  const { mutate: registerTokenMutation, isPending: isRegistering } = useRegisterPushNotificationToken();
  const messageListenerRef = useRef<(() => void) | null>(null);

  // Initialize push notifications
  useEffect(() => {
    const initializePushNotifications = async () => {
      try {
        // Check if Firebase is properly configured
        if (!validateFirebaseConfig()) {
          setState(prev => ({
            ...prev,
            isLoading: false,
            error: 'Firebase configuration is incomplete',
            isSupported: false
          }));
          return;
        }

        // Check browser support
        const isSupported = typeof window !== 'undefined' && 
                           'Notification' in window && 
                           'serviceWorker' in navigator;

        if (!isSupported) {
          setState(prev => ({
            ...prev,
            isLoading: false,
            error: 'Push notifications are not supported in this browser',
            isSupported: false
          }));
          return;
        }

        // Get current permission status
        const permission = getNotificationPermission();
        
        // Setup background message handler
        setupBackgroundMessageHandler();

        setState(prev => ({
          ...prev,
          permission,
          isSupported: true,
          isLoading: false
        }));

        // If permission is granted, get token
        if (permission === 'granted') {
          await refreshToken();
        }

      } catch (error) {
        console.error('Error initializing push notifications:', error);
        setState(prev => ({
          ...prev,
          isLoading: false,
          error: error instanceof Error ? error.message : 'Failed to initialize push notifications'
        }));
      }
    };

    initializePushNotifications();
  }, []);

  // Setup foreground message listener
  useEffect(() => {
    if (state.permission === 'granted' && state.isSupported) {
      messageListenerRef.current = onMessageListener((payload) => {
        console.log('Foreground notification received:', payload);
        
        // Show local notification when app is in foreground
        showLocalNotification(payload);
        
        // You can also trigger a custom event or update app state here
        window.dispatchEvent(new CustomEvent('pushNotificationReceived', { 
          detail: payload 
        }));
      });
    }

    return () => {
      if (messageListenerRef.current) {
        messageListenerRef.current();
        messageListenerRef.current = null;
      }
    };
  }, [state.permission, state.isSupported]);

  // Listen for service worker messages (notification clicks)
  useEffect(() => {
    const handleServiceWorkerMessage = (event: MessageEvent) => {
      if (event.data?.type === 'NOTIFICATION_CLICK') {
        const url = event.data.url;
        if (url && url !== window.location.pathname) {
          window.location.href = url;
        }
      }
    };

    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.addEventListener('message', handleServiceWorkerMessage);
      
      return () => {
        navigator.serviceWorker.removeEventListener('message', handleServiceWorkerMessage);
      };
    }
  }, []);

  const requestPermission = useCallback(async (): Promise<boolean> => {
    if (!state.isSupported) {
      return false;
    }

    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const permission = await requestNotificationPermission();
      
      setState(prev => ({ ...prev, permission, isLoading: false }));

      if (permission === 'granted') {
        await refreshToken();
        return true;
      }

      return false;
    } catch (error) {
      console.error('Error requesting permission:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to request permission'
      }));
      return false;
    }
  }, [state.isSupported]);

  const refreshToken = useCallback(async (): Promise<void> => {
    if (state.permission !== 'granted') {
      return;
    }

    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const token = await getMessagingToken();
      
      setState(prev => ({ 
        ...prev, 
        token, 
        isLoading: false,
        error: token ? null : 'Failed to get messaging token'
      }));

    } catch (error) {
      console.error('Error refreshing token:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to refresh token'
      }));
    }
  }, [state.permission]);

  const registerToken = useCallback(async (): Promise<void> => {
    if (!state.token) {
      console.warn('No token available to register');
      return;
    }

    try {
      registerTokenMutation(
        { 
          platform: 'web', 
          token: state.token 
        },
        {
          onSuccess: () => {
            setState(prev => ({ ...prev, isRegistered: true }));
            console.log('Push notification token registered successfully');
          },
          onError: (error) => {
            console.error('Failed to register push notification token:', error);
            setState(prev => ({
              ...prev,
              error: 'Failed to register token with server'
            }));
          }
        }
      );
    } catch (error) {
      console.error('Error registering token:', error);
    }
  }, [state.token, registerTokenMutation]);

  const showNotification = useCallback((payload: PushNotificationPayload): void => {
    if (state.permission === 'granted') {
      showLocalNotification(payload);
    }
  }, [state.permission]);

  const actions: PushNotificationActions = {
    requestPermission,
    refreshToken,
    registerToken,
    showNotification
  };

  return {
    ...state,
    isRegistering,
    actions
  };
};
