import type { SuperTokensConfig } from "supertokens-web-js/lib/build/types";
import EmailPassword from "supertokens-web-js/recipe/emailpassword";
import Session from "supertokens-web-js/recipe/session";
import ThirdParty from "supertokens-web-js/recipe/thirdparty";
// import { EmailPasswordPreBuiltUI } from "supertokens-auth-react/recipe/emailpassword/prebuiltui.js";

import { HOSTS_CONFIG } from "~/config/hosts";

export const authConfig = (): SuperTokensConfig => ({
  appInfo: {
    appName: "Sphere Web",
    apiDomain: HOSTS_CONFIG.auth,
    // websiteDomain: HOSTS_CONFIG.web,
    apiBasePath: HOSTS_CONFIG.isUsingProxy ? "/auth-api" : "/auth",
    // websiteBasePath: '/auth',
  },
  //   style: `
  //     [data-supertokens~=authPage] [data-supertokens~=headerSubtitle] {
  //         display: none;
  //     }
  // `,
  recipeList: [
    EmailPassword.init(),
    ThirdParty.init(),
    Session.init(),
    // Multitenancy.init({
    //   override: {
    //     functions: (oI) => {
    //       return {
    //         ...oI,
    //         getTenantId: (_input) => {
    //           const tid = localStorage.getItem("tenantId");
    //           return tid === null ? undefined : tid;
    //         },
    //       };
    //     },
    //   },
    // }),
  ],
  // getRedirectionURL: async (context) => {
  //   if (context.action === 'SUCCESS' && context.newSessionCreated) {
  //     return '/creator';
  //   }
  //   return undefined;
  // },
});

// export const PreBuiltUIList = [EmailPasswordPreBuiltUI];
