html,
body,
#root {
  height: 100%;
}
body {
  margin: 0;
}
#root {
  display: flex;
}

.str-chat__channel-list {
  flex: 1;
  background-color: #000000 !important; /* Pure black background */
  border: none;
  overflow: hidden;
}

.str-chat {
  border: none !important;
}

/* Ensure all nested elements in channel list have black background */
.str-chat__channel-list,
.str-chat__channel-list-messenger,
.str-chat__channel-list-messenger__main {
  background-color: #000000 !important;
}
.str-chat__channel-list-messenger__main {
  padding: 8px;
  border: none;
}
.str-chat__channel {
  width: 100%;
}
.str-chat__thread {
  width: 45%;
}

/* Hide default Stream Chat header */
.str-chat__channel-list-header {
  display: none !important;
}

/* Custom Channel List Container */
.channel-list-container {
  width: 30%;
  max-width: 300px;
  background-color: #000000;
  border-right: 1px solid #1f1f1f;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.custom-channel-list-header {
  background-color: rgba(0, 0, 0, 0.5);
  padding: 1.5rem 1rem 1rem 1rem;
  flex-shrink: 0;
}

/* Channel Preview Customization */
.str-chat__channel-preview {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #2d2d2d;
  transition: background-color 0.2s ease;
  cursor: pointer;
}

.str-chat__channel-preview:hover {
  background-color: #2d2d2d;
}

.str-chat__channel-preview--active {
  background-color: #3b82f6 !important;
}

.str-chat__channel-preview--active:hover {
  background-color: #2563eb !important;
}

/* Channel Preview Content */
.str-chat__channel-preview__content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.str-chat__channel-preview__content-wrapper {
  flex: 1;
  min-width: 0;
}

.str-chat__channel-preview__content-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.25rem;
}

.str-chat__channel-preview__content-name {
  color: #ffffff;
  font-weight: 600;
  font-size: 0.875rem;
  truncate: true;
}

.str-chat__channel-preview__content-time {
  color: #9ca3af;
  font-size: 0.75rem;
}

.str-chat__channel-preview__content-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.str-chat__channel-preview__content-info {
  color: #9ca3af;
  font-size: 0.8rem;
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
}

/* Avatar Customization */
.str-chat__avatar {
  border-radius: 50%;
  flex-shrink: 0;
}

/* Unread Count Badge */
.str-chat__channel-preview__unread-count {
  background-color: #ef4444;
  color: white;
  border-radius: 50%;
  min-width: 1.25rem;
  height: 1.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: 600;
}

/* Loading State */
.str-chat__channel-list__loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  color: #9ca3af;
}

/* Empty State */
.str-chat__channel-list__empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  color: #9ca3af;
  text-align: center;
}

/* Scrollbar Customization */
.str-chat__channel-list .str-chat__channel-list-messenger__main {
  background-color: #000000 !important;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */
}

.str-chat__channel-list
  .str-chat__channel-list-messenger__main::-webkit-scrollbar {
  display: none; /* WebKit */
}

/* Custom Channel Preview Styles - Now using Tailwind in React component */

/* Message Area Styling */
.str-chat__message-list {
  background-color: #000000 !important;
  padding: 1rem;
}

.str-chat__message-list-scroll {
  background-color: #000000 !important;
}

.str-chat__message-simple {
  background-color: transparent !important;
}

.str-chat__message-simple__content {
  background-color: transparent !important;
}

/* Style default message input to match mobile design */

.str-chat__message-bubble {
  background-color: #1a1a1a !important;
}

.str-chat__message-input {
  background-color: #000000 !important;
  border-top: 1px solid #374151 !important;
  padding: 1rem !important;
}

.str-chat__message-input-wrapper {
  background-color: #1f2937 !important;
  border-radius: 1.5rem !important;
  border: none !important;
}

.str-chat__message-textarea__textarea {
  background-color: #4a4a4a !important;
  color: white !important;
  border: none !important;
  border-radius: 1.5rem !important;
  padding: 0.75rem 1rem !important;
}

.str-chat__message-textarea__textarea::placeholder {
  color: #9ca3af !important;
}

/* Hide default channel header */
.str-chat__channel-header {
  display: none !important;
}

/* ===== DEFAULT MESSAGE STYLING (Mobile Design) ===== */

/* Message container - mobile style layout */
.str-chat__message-simple {
}

/* Outgoing messages (right side) */
.str-chat__message-simple--me {
}

/* Incoming messages (left side) */
.str-chat__message-simple:not(.str-chat__message-simple--me) {
}

/* Message content wrapper */
.str-chat__message-simple__content {
  border: 1px solid red !important;
  background: yellow !important;
}

/* Message bubble styling */
.str-chat__message-text {
  background-color: #1a1a1a !important;
  color: white;
  padding: 0.75rem 1rem;
  border-radius: 1.25rem;
  margin: 0;
  max-width: fit-content;
  font-size: 0.875rem;
  line-height: 1.4;
}

/* Outgoing message bubbles - darker gray */
.str-chat__message-simple--me .str-chat__message-text {
  background-color: #1a1a1a !important; /* Darker gray for outgoing */
}

/* Message text content */
.str-chat__message-text-inner {
}

/* Avatar image */
.str-chat__avatar-image {
  width: 100px !important;
  height: 100px !important;
  object-fit: cover !important;
}

/* Avatar fallback */
.str-chat__avatar-fallback {
}

/* Message status indicators (read receipts, etc.) */
.str-chat__message-simple-status {
  color: red !important;
  font-size: 0.75rem !important;
}

/* Date separators */
.str-chat__date-separator {
  font-size: 10px !important;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white !important;
  padding: 12px 0 !important;
}

.str-chat__date-separator-line {
  display: none !important;
}

/* Thread replies */
.str-chat__message-replies-count-button {
  background-color: transparent !important;
  color: #3b82f6 !important;
  border: none !important;
  padding: 0.5rem 0 !important;
  font-size: 0.75rem !important;
  margin-top: 0.5rem !important;
  display: flex !important;
  align-items: center !important;
  gap: 0.5rem !important;
}

.str-chat__message-replies-count-button:hover {
  color: #60a5fa !important;
}

.str-chat__message-simple-status-number {
  display: none !important;
}

.str-chat__avatar--message-status {
  display: none !important;
}

.str-chat__li--single {
  margin-bottom: 20px !important;
}

.str-chat__li--middle .str-chat__message > .str-chat__message-metadata {
  display: none !important;
}

.str-chat__message-metadata {
  display: flex;
  align-items: flex-end !important;
  justify-content: flex-end !important;
  gap: 0.25rem !important;
  padding-top: 2px !important;
}

.str-chat__message-simple-name {
  font-size: 10px !important;
  color: white !important;
  font-weight: 700 !important;
}

.str-chat__message-simple-timestamp {
  font-size: 8px !important;
}

/* Reaction styling - make reactions smaller and display in a row */
.str-chat__message-reactions-list {
  display: flex !important;
  background: #4a4a4a !important;
  flex-direction: row !important;
  gap: 4px !important;
  margin-top: 4px !important;
  justify-content: flex-start !important;
  flex-wrap: wrap !important;
}

.str-chat__message-reaction {
  border: 1px solid #4b5563 !important;
  border-radius: 12px !important;
  padding: 2px !important;
  font-size: 11px !important;
  min-height: auto !important;
  height: 20px !important;
  display: flex !important;
  align-items: center !important;
  gap: 3px !important;
  margin: 0 !important;
}

.str-chat__message-reaction:hover {
  background-color: #4b5563 !important;
  border-color: #6b7280 !important;
}

.str-chat__message-reaction--own {
  background-color: #3b82f6 !important;
  border-color: #2563eb !important;
  color: white !important;
}

.str-chat__message-reaction--own:hover {
  background-color: #2563eb !important;
  border-color: #1d4ed8 !important;
}

.str-chat__message-reaction-emoji {
  font-size: 12px !important;
  line-height: 1 !important;
  margin: 0 !important;
}

.str-chat__message-reaction-count {
  font-size: 10px !important;
  font-weight: 600 !important;
  line-height: 1 !important;
  margin: 0 !important;
  color: #d1d5db !important;
}

.str-chat__message-reaction--own .str-chat__message-reaction-count {
  color: white !important;
}

/* Position reactions at the end of message content */
.str-chat__message-simple-content {
  position: relative !important;
}

.str-chat__message-reactions {
  position: relative !important;
  margin-top: 6px !important;
  margin-left: 0 !important;
  width: 100% !important;
  scale: 0.8 !important;
}

/* ===== THREAD STYLING ===== */

/* Thread container */
.str-chat__thread {
  background-color: #000000 !important;
  border-left: 1px solid #374151 !important;
  width: 45% !important;
  display: flex !important;
  flex-direction: column !important;
  height: 100vh !important;
  position: relative !important;
}

/* Thread wrapper */
.str-chat__thread-wrapper {
  display: flex !important;
  flex-direction: column !important;
  height: 100% !important;
}

/* Thread header */
.str-chat__thread-header {
  background-color: #000000 !important;
  border-bottom: 1px solid #374151 !important;
  padding: 1rem !important;
  color: white !important;
  flex-shrink: 0 !important;
}

.str-chat__thread-header-details {
  color: white !important;
}

.str-chat__thread-header-title {
  color: white !important;
  font-size: 1.125rem !important;
  font-weight: 600 !important;
}

.str-chat__thread-header-subtitle {
  color: #9ca3af !important;
  font-size: 0.875rem !important;
}

/* Thread close button */
.str-chat__square-button {
  background-color: transparent !important;
  color: white !important;
  border: none !important;
  padding: 0.5rem !important;
  border-radius: 0.375rem !important;
}

.str-chat__square-button:hover {
  background-color: #374151 !important;
}

/* Thread message list */
.str-chat__thread .str-chat__message-list {
  background-color: #000000 !important;
  padding: 1rem !important;
  flex: 1 !important;
  overflow-y: auto !important;
  display: flex !important;
  flex-direction: column !important;
}

.str-chat__thread .str-chat__message-list-scroll {
  background-color: #000000 !important;
  flex: 1 !important;
  overflow-y: auto !important;
}

/* Thread messages */
.str-chat__thread .str-chat__message-simple {
  background-color: transparent !important;
  margin-bottom: 0.5rem !important;
}

.str-chat__thread .str-chat__message-text {
  background-color: #1a1a1a !important;
  color: white !important;
  padding: 0.75rem 1rem !important;
  border-radius: 1.25rem !important;
  font-size: 0.875rem !important;
  line-height: 1.4 !important;
}

/* Thread message input */
.str-chat__thread .str-chat__message-input {
  background-color: #000000 !important;
  border-top: 1px solid #374151 !important;
  padding: 1rem !important;
  flex-shrink: 0 !important;
  margin-top: auto !important;
}

.str-chat__thread .str-chat__message-input-wrapper {
  background-color: #1f2937 !important;
  border-radius: 1.5rem !important;
  border: none !important;
}

.str-chat__thread .str-chat__message-textarea__textarea {
  background-color: #4a4a4a !important;
  color: white !important;
  border: none !important;
  border-radius: 1.5rem !important;
  padding: 0.75rem 1rem !important;
}

.str-chat__thread .str-chat__message-textarea__textarea::placeholder {
  color: #9ca3af !important;
}

/* Thread message metadata */
.str-chat__thread .str-chat__message-metadata {
  display: flex !important;
  align-items: flex-end !important;
  justify-content: flex-end !important;
  gap: 0.25rem !important;
  padding-top: 2px !important;
}

.str-chat__thread .str-chat__message-simple-name {
  font-size: 10px !important;
  color: white !important;
  font-weight: 700 !important;
}

.str-chat__thread .str-chat__message-simple-timestamp {
  font-size: 8px !important;
  color: #9ca3af !important;
}

/* Thread send button */
.str-chat__thread .str-chat__send-button {
  background-color: transparent !important;
  border: none !important;
  border-radius: 50% !important;
  color: white !important;
  padding: 0.5rem !important;
  margin-left: 0.5rem !important;
}

.str-chat__thread .str-chat__send-button:hover {
  background-color: #374151 !important;
}

/* Thread avatar styling */
.str-chat__thread .str-chat__avatar-image {
  width: 32px !important;
  height: 32px !important;
  object-fit: cover !important;
}

/* Thread reactions */
.str-chat__thread .str-chat__message-reactions-list {
  display: flex !important;
  background: #4a4a4a !important;
  flex-direction: row !important;
  gap: 4px !important;
  margin-top: 4px !important;
  justify-content: flex-start !important;
  flex-wrap: wrap !important;
}

.str-chat__thread .str-chat__message-reaction {
  border: 1px solid #4b5563 !important;
  border-radius: 12px !important;
  padding: 2px !important;
  font-size: 11px !important;
  min-height: auto !important;
  height: 20px !important;
  display: flex !important;
  align-items: center !important;
  gap: 3px !important;
  margin: 0 !important;
}

/* Thread empty state */
.str-chat__thread-start {
  background-color: #000000 !important;
  color: #9ca3af !important;
  text-align: center !important;
  padding: 2rem !important;
}

/* Thread loading state */
.str-chat__thread .str-chat__loading-indicator {
  color: #9ca3af !important;
}
