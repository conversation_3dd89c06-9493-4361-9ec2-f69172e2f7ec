{"name": "sphere-web", "private": true, "type": "module", "scripts": {"build": "react-router build", "dev": "react-router dev", "start": "react-router-serve ./build/server/index.js", "typecheck": "react-router typegen && tsc"}, "dependencies": {"@react-router/node": "^7.5.3", "@react-router/serve": "^7.5.3", "@tanstack/react-query": "^5.80.6", "firebase": "^11.10.0", "getstream": "^8.8.0", "hls.js": "^1.6.5", "isbot": "^5.1.27", "lucide-react": "^0.522.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router": "^7.5.3", "stream-chat": "^9.7.0", "stream-chat-react": "^13.1.2", "supertokens-web-js": "^0.15.0"}, "devDependencies": {"@react-router/dev": "^7.5.3", "@tailwindcss/vite": "^4.1.4", "@types/node": "^20", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "tailwindcss": "^4.1.4", "typescript": "^5.8.3", "vite": "^6.3.3", "vite-tsconfig-paths": "^5.1.4"}}