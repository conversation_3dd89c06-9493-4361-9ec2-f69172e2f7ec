# Push Notifications Implementation

This document describes the push notification implementation for Sphere Web using Firebase Cloud Messaging (FCM).

## Overview

The push notification system allows the app to send real-time notifications to users even when the app is not active. It integrates with the existing notification system and provides a seamless user experience.

## Architecture

### Components

1. **Firebase Configuration** (`app/lib/firebase/config.ts`)
   - Initializes Firebase app with environment variables
   - Provides messaging instance and validation

2. **Firebase Messaging Service** (`app/lib/firebase/messaging.ts`)
   - Handles permission requests
   - Manages FCM token generation
   - Sets up foreground and background message listeners
   - Provides local notification display

3. **Service Worker** (`public/firebase-messaging-sw.js`)
   - Handles background push notifications
   - Manages notification click events
   - Provides notification actions (open/dismiss)

4. **React Hook** (`app/lib/hooks/usePushNotifications.ts`)
   - Manages push notification state
   - Provides actions for permission, token, and registration
   - Handles foreground message listening

5. **App Context Integration** (`app/lib/providers/app-context.tsx`)
   - Integrates push notifications into global app state
   - Auto-registers tokens when user is authenticated
   - Provides push notification state to all components

6. **UI Components** (`app/components/PushNotificationSettings.tsx`)
   - Permission request banner
   - Settings panel with status display
   - User-friendly permission management

## Environment Variables

Required environment variables (add to `.env`):

```env
VITE_FIREBASE_API_KEY="your-api-key"
VITE_FIREBASE_AUTH_DOMAIN="your-project.firebaseapp.com"
VITE_FIREBASE_PROJECT_ID="your-project-id"
VITE_FIREBASE_STORAGE_BUCKET="your-project.appspot.com"
VITE_FIREBASE_MESSAGING_SENDER_ID="your-sender-id"
VITE_FIREBASE_APP_ID="your-app-id"
VITE_FIREBASE_VAPID_KEY="your-vapid-key"
```

## Setup Instructions

### 1. Firebase Project Setup

1. Create a Firebase project at https://console.firebase.google.com
2. Enable Cloud Messaging in the Firebase console
3. Generate a VAPID key pair in Project Settings > Cloud Messaging
4. Add your domain to authorized domains

### 2. Environment Configuration

1. Copy the Firebase config values to your `.env` file
2. Ensure all required environment variables are set
3. The VAPID key is required for web push notifications

### 3. Backend Integration

The frontend automatically registers FCM tokens with the backend via:
- Endpoint: `POST /notifications/device-tokens`
- Payload: `{ platform: "web", token: "fcm-token" }`

Ensure your backend can:
1. Store FCM tokens for users
2. Send push notifications via Firebase Admin SDK
3. Handle token refresh/updates

## Usage

### Basic Integration

The push notification system is automatically initialized when the app starts. Users will see:

1. **Permission Banner**: Prompts users to enable notifications
2. **Settings Panel**: Shows current status and allows management
3. **Automatic Registration**: Tokens are registered when permission is granted

### Programmatic Usage

```typescript
import { useAppContext } from "~/lib/providers/app-context";

function MyComponent() {
  const { pushNotifications } = useAppContext();

  const handleEnableNotifications = async () => {
    const granted = await pushNotifications.actions.requestPermission();
    if (granted) {
      console.log('Notifications enabled!');
    }
  };

  const handleSendTestNotification = () => {
    pushNotifications.actions.showNotification({
      title: "Test Notification",
      body: "This is a test message",
      data: { link: "/notifications" }
    });
  };

  return (
    <div>
      <p>Permission: {pushNotifications.permission}</p>
      <p>Token: {pushNotifications.token ? 'Generated' : 'None'}</p>
      <p>Registered: {pushNotifications.isRegistered ? 'Yes' : 'No'}</p>
      
      <button onClick={handleEnableNotifications}>
        Enable Notifications
      </button>
      
      {pushNotifications.permission === 'granted' && (
        <button onClick={handleSendTestNotification}>
          Send Test Notification
        </button>
      )}
    </div>
  );
}
```

## Testing

### Development Testing

1. Start the development server: `npm run dev`
2. Navigate to `/notifications`
3. Use the test panel (visible only in development) to:
   - Request permissions
   - Generate and register tokens
   - Send test notifications

### Production Testing

1. Deploy the app with proper Firebase configuration
2. Test on different browsers and devices
3. Verify notifications work when app is:
   - In foreground (shows local notification)
   - In background (shows system notification)
   - Closed (shows system notification)

## Browser Support

- **Chrome/Edge**: Full support
- **Firefox**: Full support
- **Safari**: Limited support (iOS 16.4+, macOS 13+)
- **Mobile browsers**: Varies by platform

## Troubleshooting

### Common Issues

1. **"Firebase configuration is incomplete"**
   - Check all environment variables are set
   - Verify VAPID key is correct

2. **"Push notifications are not supported"**
   - Check browser compatibility
   - Ensure HTTPS is enabled (required for service workers)

3. **Token not generating**
   - Verify Firebase project settings
   - Check browser console for errors
   - Ensure service worker is registered

4. **Notifications not appearing**
   - Check browser notification settings
   - Verify service worker is active
   - Test with browser developer tools

### Debug Information

The test component provides detailed debug information including:
- Permission status
- Token generation status
- Registration status
- Error messages
- Raw FCM token for backend testing

## Security Considerations

1. **VAPID Keys**: Keep VAPID keys secure and rotate regularly
2. **Token Storage**: FCM tokens are automatically managed and refreshed
3. **Permissions**: Always request permissions with clear user consent
4. **Data**: Avoid sending sensitive data in push notification payloads

## Performance

- Service worker is lazy-loaded and cached
- Firebase SDK is loaded on-demand
- Token registration happens automatically in background
- Minimal impact on app startup time

## Future Enhancements

Potential improvements:
1. Notification categories and preferences
2. Rich notifications with images and actions
3. Notification scheduling and batching
4. Analytics and delivery tracking
5. A/B testing for notification content
